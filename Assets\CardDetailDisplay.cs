using UnityEngine;
using UnityEngine.UI;
using System.Collections.Generic;
using TMPro;

/// <summary>
/// Main controller for displaying card details
/// </summary>
public class CardDetailDisplay : MonoBehaviour
{
    // Singleton pattern
    public static CardDetailDisplay Instance { get; private set; }
    
    public enum CardSource
    {
        None,
        CardRow,        // Card from the technology deck row
        PlayerHand,     // Card from the player's hand
        PlayerPlayArea, // Card from the player's play area
        WorldCard,      // World card
        OrbitLocation   // Orbital location
    }

    [Header("Panel Coloring")]
    [SerializeField] private Image panelImage;

    [Header("Flavor Text")]
    [SerializeField] public TextMeshProUGUI flavorText;

    [Header("UI References")]
    [SerializeField] private GameObject detailPanel;
    [SerializeField] public Image cardImage;
    [SerializeField] private Button closeButton;
    
    [Header("Action Buttons")]
    [SerializeField] private Transform actionButtonContainer;
    [SerializeField] private Button primaryActionButton;
    [SerializeField] private Button secondaryActionButton;
    [SerializeField] private But<PERSON> tertiaryActionButton;
    [SerializeField] private Button button4;
    [SerializeField] private Button button5;
    [SerializeField] private Button button6;
    [SerializeField] private Button button7;
    [SerializeField] private Button button8;
    [SerializeField] private TextMeshProUGUI primaryActionText;
    [SerializeField] private TextMeshProUGUI secondaryActionText;
    [SerializeField] private TextMeshProUGUI tertiaryActionText;
    [SerializeField] private TextMeshProUGUI button4Text;
    [SerializeField] private TextMeshProUGUI button5Text;
    [SerializeField] private TextMeshProUGUI button6Text;
    [SerializeField] private TextMeshProUGUI button7Text;
    [SerializeField] private TextMeshProUGUI button8Text;

    
    [Header("Action Indicators")]
    [SerializeField] private GameObject actionIndicatorPrefab;
    [SerializeField] private Transform actionIndicatorContainer;
    [SerializeField] private Transform secondaryActionIndicatorContainer;
    [SerializeField] private Transform tertiaryActionIndicatorContainer;
    [SerializeField] private Transform button4ActionIndicatorContainer;
    [SerializeField] private Transform button5ActionIndicatorContainer;
    [SerializeField] private Transform button6ActionIndicatorContainer;
    [SerializeField] private Transform button7ActionIndicatorContainer;
    [SerializeField] private Transform button8ActionIndicatorContainer;

    
    [Header("Resource Display")]
    [SerializeField] public Transform resourceListParent;
    [SerializeField] private GameObject resourceItemPrefab;
    [SerializeField] private TextMeshProUGUI resourceSectionHeaderCost;
    [SerializeField] private TextMeshProUGUI resourceSectionHeaderEffect;
    
    [Header("Animation")]
    [SerializeField] private float animationDuration = 0.3f;
    [SerializeField] private AnimationCurve animationCurve = AnimationCurve.EaseInOut(0, 0, 1, 1);
    
    [Header("Backdrop Detection")]
    [SerializeField] private LayerMask cardLayerMask = -1;
    
    [Header("Debugging")]
    [SerializeField] private bool enableDebugLogs = false;
    
    [Header("Market UI")]
    [SerializeField] public EarthMarketUI earthMarketUI;

    // Component references
    private CardResourceManager resourceManager;
    public CardActionButtonManager buttonManager;
    public CardResourceUIManager resourceUIManager;
    private CardUIAnimator uiAnimator;
    private CardInteractionDetector interactionDetector;
    
    // Card data
    private CardData currentCardData;
    private int currentCardIndex = -1;
    private CardSource currentCardSource = CardSource.None;
    private GameObject currentSourceObject;
    
    // State tracking
    private bool isShowing = false;

    private void Awake()
    {
        if (Instance != null && Instance != this)
        {
            Destroy(gameObject);
            return;
        }
        
        Instance = this;
        
        // Initialize all components
        InitializeComponents();
        
        if (closeButton != null)
            closeButton.onClick.AddListener(Hide);
        
        if (detailPanel != null)
        {
            detailPanel.SetActive(false);
        }
        
        // Find DetailPanel if not assigned
        if (detailPanel == null)
        {
            // Try to find in direct children
            detailPanel = transform.Find("DetailPanel")?.gameObject;
            
            // Try to find in scene
            if (detailPanel == null)
            {
                detailPanel = GameObject.Find("DetailPanel");
            }
            
            if (detailPanel == null)
            {
                Debug.LogError("CardDetailDisplay: DetailPanel not found! Panel closing detection will not work correctly.");
            }
            else
            {
                if (enableDebugLogs)
                    Debug.Log($"CardDetailDisplay: Found DetailPanel {detailPanel.name}");
            }
        }
    }

    /// <summary>
    /// Initialize all component classes
    /// </summary>
    private void InitializeComponents()
    {
        // Create the resource manager
        resourceManager = new CardResourceManager();

        // Find the market UI
        EarthMarketUI marketUI = GetComponentInChildren<EarthMarketUI>(true);

        // Create the button manager
        buttonManager = new CardActionButtonManager(
            this,
            primaryActionButton,
            secondaryActionButton,
            tertiaryActionButton,
            button4,
            button5,
            button6,
            button7,
            button8,
            primaryActionText,
            secondaryActionText,
            tertiaryActionText,
            button4Text,
            button5Text,
            button6Text,
            button7Text,
            button8Text,
            actionButtonContainer,
            actionIndicatorPrefab,
            actionIndicatorContainer,
            secondaryActionIndicatorContainer,
            tertiaryActionIndicatorContainer,
            button4ActionIndicatorContainer,
            button5ActionIndicatorContainer,
            button6ActionIndicatorContainer,
            button7ActionIndicatorContainer,
            button8ActionIndicatorContainer,
            earthMarketUI);


        // Create the resource UI manager
        resourceUIManager = new CardResourceUIManager(
            resourceListParent,
            resourceItemPrefab,
            resourceSectionHeaderCost,
            resourceSectionHeaderEffect,
            resourceManager);

        // Create the UI animator
        uiAnimator = new CardUIAnimator(
            detailPanel,
            animationDuration,
            animationCurve,
            this);

        // Create the interaction detector
        interactionDetector = new CardInteractionDetector(
            this,
            cardLayerMask);
    }

    
    private void Update()
    {
        // Only process clicks when the panel is showing
        if (isShowing && Input.GetMouseButtonDown(0))
        {
            // Debug the click
            if (enableDebugLogs)
                Debug.Log($"Click detected at {Input.mousePosition} while detail panel is showing");
                
            // Instead of checking for backdrop clicks, check if we should close
            bool shouldClose = interactionDetector.ShouldCloseOnClick(Input.mousePosition);
            
            if (shouldClose)
            {
                if (enableDebugLogs)
                    Debug.Log("Click was outside panel - hiding panel");
                Hide();
            }
            else if (enableDebugLogs)
            {
                Debug.Log("Click was inside panel or on a card - keeping panel open");
            }
        }
    }
    
    /// <summary>
    /// Get the detail panel game object
    /// </summary>
    public GameObject GetDetailPanel()
    {
        return detailPanel;
    }

    /// <summary>
    /// Show card details for the given card
    /// </summary>
    public void ShowCard(CardData cardData, int cardIndex = -1, CardSource source = CardSource.CardRow, GameObject sourceObject = null)
    {
        if (cardData == null)
        {
            Debug.LogError("CardDetailDisplay: cardData is null!");
            return;
        }

        if (detailPanel == null)
        {
            Debug.LogError("CardDetailDisplay: detailPanel is null!");
            return;
        }

        if (enableDebugLogs)
            Debug.Log($"Showing card: {cardData.Name}, index: {cardIndex}, source: {source}");

        // Reset UI elements that might have been modified by previous cards
        buttonManager.ResetDetailPanelUI();

        // Hide market when switching to any non-Earth card
        if (earthMarketUI != null)
        {
            earthMarketUI.HideMarket();
        }

        // Store the card data
        currentCardData = cardData;
        currentCardIndex = cardIndex;
        currentCardSource = source;
        currentSourceObject = sourceObject;

        // Change panel color based on card type
        ChangePanelColor(cardData.Type);

        //Pass data to CardToHandSystem
        CardToHandSystem cardToHandSystem = CardToHandSystem.Instance;
        if (cardToHandSystem != null)
        {
            cardToHandSystem.SetCardData(cardData, cardIndex, sourceObject);
        }

        // Update UI elements
        UpdateCardVisuals();
        UpdateFlavorText();

        // Update component states
        buttonManager.SetCardData(cardData, cardIndex, source, sourceObject);
        buttonManager.ConfigureButtons();

        resourceUIManager.SetCardData(cardData);

        // Check if this is a world or has aerobraking capability or high radiation
        bool hasAerobraking = false;
        bool hasHighRadiation = false;

        if (source == CardSource.WorldCard && sourceObject != null)
        {
            // Try to get WorldCardVisual first
            WorldCardVisual worldCardVisual = sourceObject.GetComponent<WorldCardVisual>();
            if (worldCardVisual != null)
            {
                hasAerobraking = worldCardVisual.CanAerobrake();

                // Get the celestial body to check for high radiation
                GameObject celestialBody = worldCardVisual.GetCelestialBody();
                if (celestialBody != null)
                {
                    PlanetBody planetBody = celestialBody.GetComponent<PlanetBody>();
                    if (planetBody != null)
                    {
                        hasHighRadiation = planetBody.HighRadiation;
                    }
                }
            }
            else
            {
                // If no WorldCardVisual, try to get PlanetBody directly
                PlanetBody planetBody = sourceObject.GetComponent<PlanetBody>();
                if (planetBody != null)
                {
                    hasAerobraking = planetBody.CanAerobrake;
                    hasHighRadiation = planetBody.HighRadiation;
                }
            }

            // Handle world resources display
            WorldResourceDisplay resourceDisplay = GetComponent<WorldResourceDisplay>();
            if (resourceDisplay != null)
            {
                // Show resources for world cards and orbital locations
                if ((source == CardSource.WorldCard || source == CardSource.OrbitLocation) && sourceObject != null)
                {
                    if (source == CardSource.WorldCard)
                    {
                        // For world cards, get the celestial body
                        WorldCardVisual worldCard = sourceObject.GetComponent<WorldCardVisual>();
                        if (worldCard != null)
                        {
                            GameObject celestialBody = worldCard.GetCelestialBody();

                            if (celestialBody != null)
                            {
                                int currentPlayer = GameManager.Instance.CurrentPlayerIndex;
                                resourceDisplay.DisplayWorldResources(celestialBody, currentPlayer);
                            }
                        }
                        else if (sourceObject.GetComponent<PlanetBody>() != null)
                        {
                            // Direct click on planet
                            int currentPlayer = GameManager.Instance.CurrentPlayerIndex;
                            resourceDisplay.DisplayWorldResources(sourceObject, currentPlayer);
                        }
                    }
                    else if (source == CardSource.OrbitLocation)
                    {
                        // For orbital locations, display resources at the orbit location
                        int currentPlayer = GameManager.Instance.CurrentPlayerIndex;
                        resourceDisplay.DisplayWorldResources(sourceObject, currentPlayer);
                    }
                }
                else
                {
                    // Clear resources display for non-world/non-orbit cards
                    resourceDisplay.ClearDisplay();
                }
            }
        }
        else
        {
            // Clear world resources for non-world cards
            WorldResourceDisplay resourceDisplay = GetComponent<WorldResourceDisplay>();
            if (resourceDisplay != null)
            {
                resourceDisplay.ClearDisplay();
            }
        }

        // Get orbital location name for reminder text if applicable
        string orbitLocationName = null;
        if (source == CardSource.OrbitLocation && sourceObject != null)
        {
            OrbitLocation orbitComponent = sourceObject.GetComponent<OrbitLocation>();
            if (orbitComponent != null)
            {
                orbitLocationName = FormatOrbitLocationName(orbitComponent.Name);
            }
        }

        // Check if this is an instantiated ship or module that should force the resources section to show
        bool forceShowResourcesSection = IsInstantiatedShipOrModule(currentCardData, sourceObject, source);

        resourceUIManager.UpdateResourceLists(hasAerobraking, hasHighRadiation, orbitLocationName, forceShowResourcesSection);

        // Show the panel with animation
        detailPanel.SetActive(true);
        uiAnimator.ShowAnimation();

        isShowing = true;
    }

    /// <summary>
    /// Check if this is an instantiated ship or module that should force the resources section to show
    /// </summary>
    private bool IsInstantiatedShipOrModule(CardData cardData, GameObject sourceObject, CardSource source)
    {
        // Only check for PlayerPlayArea source (instantiated objects)
        if (source != CardSource.PlayerPlayArea)
            return false;

        // Check if the source object has ship or module visual components (indicating it's instantiated)
        if (sourceObject != null)
        {
            if (sourceObject.GetComponent<ShipCardVisual>() != null ||
                sourceObject.GetComponent<ModuleCardVisual>() != null)
            {
                return true;
            }
        }

        // Also check card type as a fallback
        if (cardData != null)
        {
            string cardType = cardData.Type?.ToLower();
            if (cardType == "ship" ||
                cardType == "power" ||
                cardType == "processor" ||
                cardType == "extractor" ||
                cardType == "wonder" ||
                cardType == "habitation")
            {
                return true;
            }
        }

        return false;
    }

    private void ChangePanelColor(string cardType)
    {
        if (panelImage == null) return;

        Color targetColor = Color.white; // Default color

        switch (cardType?.ToLower())
        {
            case "habitation":
                targetColor = new Color(1f, 0.65f, 0f); // Orange
                break;
            case "processor":
                targetColor = new Color(0f, 0.5f, 1f); // Blue
                break;
            case "ship":
                targetColor = new Color(1f, 0f, 0f); // Red
                break;
            case "power":
                targetColor = new Color(1f, 1f, 0f); // Yellow
                break;
            case "wonder":
                targetColor = new Color(0f, 0.8f, 0f); // Green
                break;
            case "extractor":
                targetColor = new Color(0.6f, 0.3f, 0f); // Brown
                break;
            case "upgrade":
                targetColor = new Color(0.5f, 0f, 1f); // Purple
                break;
            case "initiative":
                targetColor = Color.white; // White
                break;
            default:
                targetColor = Color.white; // Default white
                break;
        }

        panelImage.color = targetColor;
    }

    /// <summary>
    /// Hide the card detail panel
    /// </summary>
    public void Hide()
    {
        if (enableDebugLogs)
            Debug.Log("Hiding card detail panel");

        if (isShowing)
        {
            uiAnimator.HideAnimation();

            // Clear any displayed resources
            WorldResourceDisplay resourceDisplay = GetComponent<WorldResourceDisplay>();
            if (resourceDisplay != null)
            {
                resourceDisplay.ClearDisplay();
            }
        }

        // Clean up processor icons and reset UI
        buttonManager.ResetDetailPanelUI();

        if (earthMarketUI != null)
        {
            earthMarketUI.HideMarket();
        }

        isShowing = false;
    }


    /// <summary>
    /// Refresh the action buttons based on current resources
    /// </summary>
    public void RefreshButtons()
    {
        if (buttonManager != null)
        {
            buttonManager.ConfigureButtons();
        }
    }


    /// <summary>
    /// Update the card visuals (image, title, etc.)
    /// </summary>
    private void UpdateCardVisuals()
    {
        // For world cards, use special handling
        if (currentCardSource == CardSource.WorldCard)
        {
            UpdateCardVisualsForWorldCard(currentCardData);
            return;
        }

        // For orbital locations, use similar handling to world cards
        if (currentCardSource == CardSource.OrbitLocation)
        {
            UpdateCardVisualsForOrbitLocation(currentCardData);
            return;
        }

        // Load and display card image
        if (cardImage != null)
        {
            string cardNameFormatted = currentCardData.Name.ToLower().Replace(" ", "");
            string imagePath = $"Cards/{currentCardData.Tier}{cardNameFormatted}";

            Texture2D cardTexture = Resources.Load<Texture2D>(imagePath);

            if (cardTexture != null)
            {
                cardImage.sprite = Sprite.Create(
                    cardTexture,
                    new Rect(0, 0, cardTexture.width, cardTexture.height),
                    new Vector2(0.5f, 0.5f)
                );
            }
            else
            {
                Debug.LogWarning($"CardDetailDisplay: Could not load image from {imagePath}");
            }
        }
        else
        {
            Debug.LogWarning("CardDetailDisplay: cardImage UI component is null");
        }
    }

    private void UpdateCardVisualsForWorldCard(CardData worldCardData)
    {
        if (cardImage != null && worldCardData != null)
        {
            // For world cards, we need to find the texture differently
            string planetName = worldCardData.Name.ToLower();

            // Try to load based on planet name
            string imagePath = $"Cards/Planets/{planetName}";
            Texture2D cardTexture = Resources.Load<Texture2D>(imagePath);

            if (cardTexture != null)
            {
                cardImage.sprite = Sprite.Create(
                    cardTexture,
                    new Rect(0, 0, cardTexture.width, cardTexture.height),
                    new Vector2(0.5f, 0.5f)
                );
            }
            else
            {
                Debug.LogWarning($"Could not find texture for world card: {worldCardData.Name} (tried path: Cards/Planets/{planetName})");
            }
        }
    }

    private void UpdateCardVisualsForOrbitLocation(CardData orbitCardData)
    {
        if (cardImage != null && orbitCardData != null)
        {
            // For orbital locations, use the single "loworbitabove.png" texture
            string imagePath = "Cards/Planets/loworbitabove";
            Texture2D cardTexture = Resources.Load<Texture2D>(imagePath);

            if (cardTexture != null)
            {
                cardImage.sprite = Sprite.Create(
                    cardTexture,
                    new Rect(0, 0, cardTexture.width, cardTexture.height),
                    new Vector2(0.5f, 0.5f)
                );
            }
            else
            {
                Debug.LogWarning($"Could not find texture for orbit location: {orbitCardData.Name} (tried path: Cards/Planets/loworbitabove)");
            }
        }
    }


    /// <summary>
    /// Update the flavor text
    /// </summary>
    private void UpdateFlavorText()
    {
        if (flavorText == null || currentCardData == null)
            return;

        // Create a list of available flavor texts
        List<string> availableTexts = new List<string>();
        List<string> textTypes = new List<string>();

        // Check each flavor text type and add if available
        if (!string.IsNullOrEmpty(currentCardData.SolEncyclopedia))
        {
            availableTexts.Add(currentCardData.SolEncyclopedia);
            textTypes.Add("Sol Encyclopedia");
        }

        if (!string.IsNullOrEmpty(currentCardData.RealQuote))
        {
            availableTexts.Add(currentCardData.RealQuote);
            textTypes.Add("Earth Quote");
        }

        if (!string.IsNullOrEmpty(currentCardData.SciFiQuote))
        {
            availableTexts.Add(currentCardData.SciFiQuote);
            textTypes.Add("Sci-Fi");
        }

        // Show or hide flavor text elements based on availability
        bool hasFlavorText = availableTexts.Count > 0;
        flavorText.gameObject.SetActive(hasFlavorText);

        // Choose a random flavor text if any are available
        if (hasFlavorText)
        {
            // Add time-based randomization - changes the selection more frequently
            int randomSeed = (int)(Time.time * 100) + currentCardData.Name.GetHashCode();
            UnityEngine.Random.InitState(randomSeed);
            int randomIndex = UnityEngine.Random.Range(0, availableTexts.Count);

            string text = availableTexts[randomIndex];
            string textType = textTypes[randomIndex];

            // Style the text based on type
            switch (textType)
            {
                case "Sol Encyclopedia":
                    flavorText.fontStyle = FontStyles.Italic;
                    flavorText.color = new Color(0.8f, 0.8f, 1.0f); // Light blue
                    break;

                case "Earth Quote":
                    flavorText.fontStyle = FontStyles.Italic;
                    flavorText.color = new Color(0.9f, 0.9f, 0.7f); // Light yellow
                    break;

                case "Sci-Fi":
                    flavorText.fontStyle = FontStyles.Italic;
                    flavorText.color = new Color(0.7f, 0.9f, 0.7f); // Light green
                    break;
            }

            // Convert any line breaks to <br> tags
            text = text.Replace("\r\n", "<br>").Replace("\n", "<br>");

            // Set the text
            flavorText.text = text;
        }
        else
        {
            // No flavor text available for this card
            flavorText.text = "";
        }
    }

    /// <summary>
    /// Format orbital location name to add spaces (e.g., "LowEarthOrbit" -> "Low Earth Orbit")
    /// </summary>
    private string FormatOrbitLocationName(string orbitName)
    {
        if (string.IsNullOrEmpty(orbitName))
            return orbitName;

        // Handle the common pattern: "Low[Planet]Orbit"
        if (orbitName.StartsWith("Low") && orbitName.EndsWith("Orbit"))
        {
            // Extract the planet name from between "Low" and "Orbit"
            string planetName = orbitName.Substring(3, orbitName.Length - 8); // Remove "Low" and "Orbit"
            return $"Low {planetName} Orbit";
        }

        // For other cases, just return the original name
        return orbitName;
    }
}